package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import com.mercaso.ims.domain.category.enums.CategoryStatus;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class UpdateCategoryCommand extends BaseCommand {


    private UUID ancestorCategoryId;

    private String categoryName;

    private Integer sortOrder;

    private CategoryStatus categoryStatus;

    private String icon;

    private String description;


    public String getCategoryName() {
        return categoryName != null ? categoryName.trim() : null;
    }
}
