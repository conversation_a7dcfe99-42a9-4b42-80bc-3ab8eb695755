package com.mercaso.ims.domain.difyworkflowsrecord.enums;

import lombok.Getter;

import java.util.Optional;


@Getter
public enum WorkflowStatus {
    SUCCEEDED("succeeded"),
    FAILED("failed"),
    STOPPED("stopped");

    private final String statusName;

    WorkflowStatus(String statusName) {
        this.statusName = statusName;
    }

    public static Optional<WorkflowStatus> fromString(String statusName) {
        for (WorkflowStatus status : values()) {
            if (status.statusName.equals(statusName)) {
                return Optional.of(status);
            }
        }
        return Optional.empty();
    }
}
