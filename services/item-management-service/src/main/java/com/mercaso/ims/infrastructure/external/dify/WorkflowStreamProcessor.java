package com.mercaso.ims.infrastructure.external.dify;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mercaso.ims.infrastructure.external.dify.enums.WorkflowEvent;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Optional;

import static com.mercaso.ims.infrastructure.external.dify.DifyStaticConstant.*;

@Slf4j
public class WorkflowStreamProcessor {

    private String workflowRunId;
    private ObjectNode finalResult;

    public ObjectNode processStream(InputStream inputStream) throws IOException {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            processStreamLines(reader);
            return getFinalResult();
        }
    }

    private void processStreamLines(BufferedReader reader) throws IOException {
        String line;
        while ((line = reader.readLine()) != null) {
            if (isEmptyLine(line)) continue;

            if (isDataLine(line)) {
                processDataLine(line);
            }
        }
    }

    private boolean isEmptyLine(String line) {
        return line.trim().isEmpty();
    }

    private boolean isDataLine(String line) {
        return line.startsWith(DATA_PREFIX);
    }

    private void processDataLine(String line) {
        String jsonData = line.substring(DATA_PREFIX.length());

        if (DONE_MARKER.equals(jsonData.trim())) {
            log.info("SSE stream completed for workflow: {}", workflowRunId);
            return;
        }

        try {
            JsonNode jsonLine = SerializationUtils.standardObjectMapper().readTree(jsonData);
            processJsonEvent(jsonLine);
        } catch (Exception e) {
            log.warn("Failed to parse SSE response line: {}", jsonData, e);
        }
    }

    private void processJsonEvent(JsonNode jsonLine) {
        String eventName = jsonLine.path("event").asText();
        Optional<WorkflowEvent> event = WorkflowEvent.fromString(eventName);

        if (event.isEmpty()) {
            log.info("Unknown workflow event: {}", eventName);
            return;
        }

        switch (event.get()) {
            case WORKFLOW_STARTED:
                workflowRunId = jsonLine.path(WORKFLOW_RUN_ID).asText(null);
                log.info("Workflow started with ID: {}", workflowRunId);
                break;
            case WORKFLOW_SUCCEEDED, WORKFLOW_FINISHED:
                log.info("Workflow completed successfully: {}", workflowRunId);
                finalResult = buildSuccessResultFromEvent(jsonLine);
                break;
            case WORKFLOW_FAILED, ERROR:
                log.error("Workflow failed: {}", workflowRunId);
                String error = extractErrorMessage(jsonLine);
                finalResult = createResultNode("failed", workflowRunId, error);
                break;
        }
    }

    private String extractErrorMessage(JsonNode jsonLine) {
        return jsonLine.path("data").path("error")
                .asText("Workflow execution failed");
    }

    private ObjectNode buildSuccessResultFromEvent(JsonNode sseEvent) {
        JsonNode data = sseEvent.path("data");
        return new ResultBuilder(workflowRunId)
                .withTokens(data.path("total_tokens").asInt())
                .withSteps(data.path("total_steps").asInt())
                .withElapsedTime(data.path("elapsed_time").asDouble())
                .withOutputs(data.path("outputs"))
                .withStatus("succeeded")
                .build();
    }

    private ObjectNode getFinalResult() throws IOException {
        if (finalResult != null) {
            log.info("Workflow completed with status: {}", finalResult.get("status"));
            return finalResult;
        } else if (workflowRunId != null) {
            log.warn("Workflow started but no completion event received: {}", workflowRunId);
            return createResultNode("timeout", workflowRunId,
                    "No completion event received in SSE stream");
        } else {
            log.error("No workflow_run_id found in SSE stream");
            throw new IOException("Failed to get workflow result from SSE stream");
        }
    }

    private ObjectNode createResultNode(String status, String workflowRunId, String error) {
        ResultBuilder builder = new ResultBuilder(workflowRunId).withStatus(status);
        if (error != null) {
            builder.withError(error);
        }
        return builder.build();
    }
}
