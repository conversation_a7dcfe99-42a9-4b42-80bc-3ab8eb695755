package com.mercaso.ims.infrastructure.repository.difyworkflowrecord.jpa.dataobject;

import com.mercaso.ims.domain.difyworkflowsrecord.enums.WorkflowStatus;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import java.util.UUID;

@Entity
@Table(name = "dify_workflow_record")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update dify_workflow_record set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class DifyWorkflowRecordDo extends BaseDo {

    @Column(name = "item_id")
    private UUID itemId;

    @Column(name = "sku_number")
    private String skuNumber;

    @Column(name = "total_tokens")
    private Long totalTokens;

    @Column(name = "total_steps")
    private Integer totalSteps;

    @Column(name = "elapsed_time")
    private Double elapsedTime;

    @Column(name = "workflow_id")
    private UUID workflowId;

    @Column(name = "result")
    private String result;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private WorkflowStatus status;
}
