package com.mercaso.ims.infrastructure.external.dify;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import lombok.extern.slf4j.Slf4j;

import static com.mercaso.ims.infrastructure.external.dify.DifyStaticConstant.OUTPUT;
import static com.mercaso.ims.infrastructure.external.dify.DifyStaticConstant.WORKFLOW_RUN_ID;

@Slf4j
public class ResultBuilder {
    private final ObjectNode result = SerializationUtils.standardObjectMapper().createObjectNode();

    public ResultBuilder(String workflowRunId) {
        result.put(WORKFLOW_RUN_ID, workflowRunId);
    }

    public ResultBuilder withStatus(String status) {
        result.put("status", status);
        return this;
    }

    public ResultBuilder withTokens(int tokens) {
        result.put("total_tokens", tokens);
        return this;
    }

    public ResultBuilder withSteps(int steps) {
        result.put("total_steps", steps);
        return this;
    }

    public ResultBuilder withElapsedTime(double elapsedTime) {
        result.put("elapsed_time", elapsedTime);
        return this;
    }

    public ResultBuilder withError(String error) {
        result.put("error", error);
        return this;
    }

    public ResultBuilder withOutputs(JsonNode outputs) {
        processOutputs(result, outputs);
        return this;
    }

    private void processOutputs(ObjectNode resultJson, JsonNode outputs) {
        if (outputs == null || outputs.isMissingNode()) {
            return;
        }

        if (outputs.isObject()) {
            String outputJsonString = SerializationUtils.serialize(outputs);
            resultJson.put(OUTPUT, outputJsonString);
        } else if (outputs.isTextual()) {
            try {
                JsonNode parsed = SerializationUtils.readTree(outputs.asText());
                if (parsed.isObject()) {
                    String outputJsonString = SerializationUtils.serialize(outputs);
                    resultJson.put(OUTPUT, outputJsonString);
                } else {
                    resultJson.set(OUTPUT, outputs);
                }
            } catch (Exception e) {
                log.warn("Failed to parse output text: {}", outputs.asText(), e);
                resultJson.set(OUTPUT, outputs);
            }
        }
    }

    public ObjectNode build() {
        return result;
    }
}
