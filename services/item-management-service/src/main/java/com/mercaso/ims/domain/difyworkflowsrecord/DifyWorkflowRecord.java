package com.mercaso.ims.domain.difyworkflowsrecord;

import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.domain.difyworkflowsrecord.enums.WorkflowStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

@Slf4j
@Getter
@Setter
@ToString
@SuperBuilder
public class DifyWorkflowRecord extends BaseDomain {
    private final UUID id;

    private UUID itemId;

    private String skuNumber;

    private Long totalTokens;

    private Integer totalSteps;

    private Double elapsedTime;

    private UUID workflowId;

    private String result;

    private WorkflowStatus status;

}
