package com.mercaso.ims.infrastructure.external.dify;

import java.io.*;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mercaso.ims.infrastructure.client.HttpClient;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyWorkflowResult;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class DifyApiClient {

  private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

  private final HttpClient client;

  @Value("${dify.api.api-key}")
  private String apiKey;

  @Value("${dify.api.base-url}")
  private String baseUrl;


  public DifyWorkflowResult callDifyWorkflow(String key, String value) throws IOException {
    ObjectNode payload = createWorkflowPayload(key, value);
    Request request = buildWorkflowRequest(payload);

    try (Response response = client.execute(request)) {
      validateResponse(response);
      ObjectNode jsonNodes = extractWorkflowResult(response);
      return SerializationUtils.treeToValue(jsonNodes, DifyWorkflowResult.class);
    }
  }

  private ObjectNode createWorkflowPayload(String key, String value) {
    log.info("[createWorkflowPayload] key:{}, value:{}",key, value);
    ObjectNode payload = SerializationUtils.standardObjectMapper().createObjectNode();
    ObjectNode inputs = SerializationUtils.standardObjectMapper().createObjectNode();
    inputs.put(key, value);
    payload.set("inputs", inputs);
    payload.put("response_mode", "streaming");
    payload.put("user", SecurityUtil.getUserName());
    return payload;
  }

  private Request buildWorkflowRequest(ObjectNode payload) throws IOException {
    return new Request.Builder()
        .url(baseUrl + "/workflows/run")
        .addHeader("Authorization", "Bearer " + apiKey)
        .addHeader("Content-Type", "application/json")
        .post(RequestBody.create(SerializationUtils.standardObjectMapper().writeValueAsString(payload), JSON))
        .build();
  }

  private void validateResponse(Response response) {
    if (response == null || !response.isSuccessful()) {
      int statusCode = response != null ? response.code() : -1;
      log.error("DifyApiClient call failed with status code {}", statusCode);
      throw new ImsBusinessException("API call failed with status: " + statusCode);
    }
  }

  private ObjectNode extractWorkflowResult(Response response) throws IOException {
    if (response.body() == null) {
      throw new ImsBusinessException("Response body is null");
    }

    WorkflowStreamProcessor processor = new WorkflowStreamProcessor();
    return processor.processStream(response.body().byteStream());
  }
}
