package com.mercaso.ims.infrastructure.external.dify;

import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;

class WorkflowStreamProcessorTest {

    private WorkflowStreamProcessor processor;

    @BeforeEach
    void setUp() {
        processor = new WorkflowStreamProcessor();
    }

    @Test
    void processStream_SuccessfulWorkflow_ReturnsSuccessResult() throws IOException {
        // Arrange
        String streamData = """
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-123"}
            
            data: {"event": "workflow_succeeded", "data": {"total_tokens": 100, "total_steps": 5, "elapsed_time": 2.5, "outputs": {"result": "success"}}}
            
            data: [DONE]
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-123", result.get("workflow_run_id").asText());
        assertEquals("succeeded", result.get("status").asText());
        assertEquals(100, result.get("total_tokens").asInt());
        assertEquals(5, result.get("total_steps").asInt());
        assertEquals(2.5, result.get("elapsed_time").asDouble());
        assertTrue(result.has("output"));
    }

    @Test
    void processStream_WorkflowFinished_ReturnsSuccessResult() throws IOException {
        // Arrange
        String streamData = """
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-456"}
            
            data: {"event": "workflow_finished", "data": {"total_tokens": 200, "total_steps": 10, "elapsed_time": 5.0, "outputs": {"result": "finished"}}}
            
            data: [DONE]
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-456", result.get("workflow_run_id").asText());
        assertEquals("succeeded", result.get("status").asText());
        assertEquals(200, result.get("total_tokens").asInt());
        assertEquals(10, result.get("total_steps").asInt());
        assertEquals(5.0, result.get("elapsed_time").asDouble());
    }

    @Test
    void processStream_FailedWorkflow_ReturnsFailedResult() throws IOException {
        // Arrange
        String streamData = """
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-789"}
            
            data: {"event": "workflow_failed", "data": {"error": "Custom error message"}}
            
            data: [DONE]
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-789", result.get("workflow_run_id").asText());
        assertEquals("failed", result.get("status").asText());
        assertEquals("Custom error message", result.get("error").asText());
    }

    @Test
    void processStream_ErrorEvent_ReturnsFailedResult() throws IOException {
        // Arrange
        String streamData = """
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-error"}
            
            data: {"event": "error", "data": {"error": "System error occurred"}}
            
            data: [DONE]
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-error", result.get("workflow_run_id").asText());
        assertEquals("failed", result.get("status").asText());
        assertEquals("System error occurred", result.get("error").asText());
    }

    @Test
    void processStream_NoErrorMessage_UsesDefaultErrorMessage() throws IOException {
        // Arrange
        String streamData = """
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-no-error"}
            
            data: {"event": "workflow_failed", "data": {}}
            
            data: [DONE]
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-no-error", result.get("workflow_run_id").asText());
        assertEquals("failed", result.get("status").asText());
        assertEquals("Workflow execution failed", result.get("error").asText());
    }

    @Test
    void processStream_StartedButNoCompletion_ReturnsTimeoutResult() throws IOException {
        // Arrange
        String streamData = """
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-timeout"}
            
            data: [DONE]
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-timeout", result.get("workflow_run_id").asText());
        assertEquals("timeout", result.get("status").asText());
        assertEquals("No completion event received in SSE stream", result.get("error").asText());
    }

    @Test
    void processStream_NoWorkflowStarted_ThrowsIOException() {
        // Arrange
        String streamData = """
            data: {"event": "unknown_event", "data": {}}
            
            data: [DONE]
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act & Assert
        IOException exception = assertThrows(IOException.class, 
            () -> processor.processStream(inputStream));
        
        assertEquals("Failed to get workflow result from SSE stream", exception.getMessage());
    }

    @Test
    void processStream_EmptyStream_ThrowsIOException() {
        // Arrange
        String streamData = "";
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act & Assert
        IOException exception = assertThrows(IOException.class, 
            () -> processor.processStream(inputStream));
        
        assertEquals("Failed to get workflow result from SSE stream", exception.getMessage());
    }

    @Test
    void processStream_OnlyEmptyLines_ThrowsIOException() {
        // Arrange
        String streamData = """
            
            
            
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act & Assert
        IOException exception = assertThrows(IOException.class, 
            () -> processor.processStream(inputStream));
        
        assertEquals("Failed to get workflow result from SSE stream", exception.getMessage());
    }

    @Test
    void processStream_InvalidJsonData_IgnoresInvalidLines() throws IOException {
        // Arrange
        String streamData = """
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-invalid"}
            
            data: invalid json data
            
            data: {"event": "workflow_succeeded", "data": {"total_tokens": 50, "total_steps": 3, "elapsed_time": 1.5, "outputs": {}}}
            
            data: [DONE]
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-invalid", result.get("workflow_run_id").asText());
        assertEquals("succeeded", result.get("status").asText());
        assertEquals(50, result.get("total_tokens").asInt());
    }

    @Test
    void processStream_UnknownEvent_IgnoresEvent() throws IOException {
        // Arrange
        String streamData = """
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-unknown"}
            
            data: {"event": "unknown_event", "data": {"some": "data"}}
            
            data: {"event": "workflow_succeeded", "data": {"total_tokens": 75, "total_steps": 4, "elapsed_time": 2.0, "outputs": {"result": "done"}}}
            
            data: [DONE]
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-unknown", result.get("workflow_run_id").asText());
        assertEquals("succeeded", result.get("status").asText());
        assertEquals(75, result.get("total_tokens").asInt());
    }

    @Test
    void processStream_NonDataLines_IgnoresNonDataLines() throws IOException {
        // Arrange
        String streamData = """
            event: workflow_started
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-mixed"}
            
            id: 123
            retry: 3000
            
            data: {"event": "workflow_succeeded", "data": {"total_tokens": 25, "total_steps": 2, "elapsed_time": 1.0, "outputs": {}}}
            
            data: [DONE]
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-mixed", result.get("workflow_run_id").asText());
        assertEquals("succeeded", result.get("status").asText());
        assertEquals(25, result.get("total_tokens").asInt());
    }

    @Test
    void processStream_MissingDataFields_HandlesGracefully() throws IOException {
        // Arrange
        String streamData = """
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-missing"}
            
            data: {"event": "workflow_succeeded", "data": {"total_tokens": 30}}
            
            data: [DONE]
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-missing", result.get("workflow_run_id").asText());
        assertEquals("succeeded", result.get("status").asText());
        assertEquals(30, result.get("total_tokens").asInt());
        assertEquals(0, result.get("total_steps").asInt()); // Default value for missing field
        assertEquals(0.0, result.get("elapsed_time").asDouble()); // Default value for missing field
    }

    @Test
    void processStream_MultipleCompletionEvents_UsesLastEvent() throws IOException {
        // Arrange
        String streamData = """
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-multiple"}
            
            data: {"event": "workflow_failed", "data": {"error": "First error"}}
            
            data: {"event": "workflow_succeeded", "data": {"total_tokens": 40, "total_steps": 3, "elapsed_time": 1.5, "outputs": {}}}
            
            data: [DONE]
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-multiple", result.get("workflow_run_id").asText());
        assertEquals("succeeded", result.get("status").asText());
        assertEquals(40, result.get("total_tokens").asInt());
        assertFalse(result.has("error")); // Should not have error since last event was success
    }
}
