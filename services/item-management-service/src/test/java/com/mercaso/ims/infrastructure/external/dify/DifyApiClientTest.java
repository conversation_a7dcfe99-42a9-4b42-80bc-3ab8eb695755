package com.mercaso.ims.infrastructure.external.dify;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mercaso.ims.infrastructure.client.HttpClient;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyWorkflowResult;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DifyApiClientTest {

    @Mock
    private HttpClient httpClient;

    @Mock
    private Response response;

    @Mock
    private ResponseBody responseBody;

    @InjectMocks
    private DifyApiClient difyApiClient;

    private static final String TEST_API_KEY = "test-api-key";
    private static final String TEST_BASE_URL = "https://api.dify.ai";
    private static final String TEST_KEY = "test-key";
    private static final String TEST_VALUE = "test-value";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(difyApiClient, "apiKey", TEST_API_KEY);
        ReflectionTestUtils.setField(difyApiClient, "baseUrl", TEST_BASE_URL);
    }

    @Test
    void callDifyWorkflow_Success() throws IOException {
        // Arrange
        String mockStreamData = """
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-id"}
            
            data: {"event": "workflow_succeeded", "data": {"total_tokens": 100, "total_steps": 5, "elapsed_time": 2.5, "outputs": {"result": "success"}}}
            
            data: [DONE]
            """;
        
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.byteStream()).thenReturn(new ByteArrayInputStream(mockStreamData.getBytes()));

        // Act
        DifyWorkflowResult result = difyApiClient.callDifyWorkflow(TEST_KEY, TEST_VALUE);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-id", result.getWorkflowRunId());
        assertEquals(100L, result.getTotalTokens());
        assertEquals(5, result.getTotalSteps());
        assertEquals(2.5, result.getElapsedTime());
        assertEquals("succeeded", result.getStatus());
        
        verify(httpClient).execute(any(Request.class));
        verify(response).isSuccessful();
        verify(response).body();
        verify(responseBody).byteStream();
    }

    @Test
    void callDifyWorkflow_ResponseNotSuccessful_ThrowsException() throws IOException {
        // Arrange
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);
        when(response.code()).thenReturn(400);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class, 
            () -> difyApiClient.callDifyWorkflow(TEST_KEY, TEST_VALUE));
        
        assertTrue(exception.getMessage().contains("API call failed with status: 400"));
        verify(httpClient).execute(any(Request.class));
        verify(response).isSuccessful();
        verify(response).code();
    }

    @Test
    void callDifyWorkflow_NullResponse_ThrowsException() throws IOException {
        // Arrange
        when(httpClient.execute(any(Request.class))).thenReturn(null);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class, 
            () -> difyApiClient.callDifyWorkflow(TEST_KEY, TEST_VALUE));
        
        assertTrue(exception.getMessage().contains("API call failed with status: -1"));
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void callDifyWorkflow_NullResponseBody_ThrowsException() throws IOException {
        // Arrange
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(null);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class, 
            () -> difyApiClient.callDifyWorkflow(TEST_KEY, TEST_VALUE));
        
        assertEquals("Response body is null", exception.getMessage());
        verify(httpClient).execute(any(Request.class));
        verify(response).isSuccessful();
        verify(response).body();
    }

    @Test
    void callDifyWorkflow_HttpClientThrowsIOException_PropagatesException() throws IOException {
        // Arrange
        when(httpClient.execute(any(Request.class))).thenThrow(new IOException("Network error"));

        // Act & Assert
        IOException exception = assertThrows(IOException.class, 
            () -> difyApiClient.callDifyWorkflow(TEST_KEY, TEST_VALUE));
        
        assertEquals("Network error", exception.getMessage());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void callDifyWorkflow_WorkflowFailed() throws IOException {
        // Arrange
        String mockStreamData = """
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-id"}
            
            data: {"event": "workflow_failed", "data": {"error": "Workflow execution failed"}}
            
            data: [DONE]
            """;
        
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.byteStream()).thenReturn(new ByteArrayInputStream(mockStreamData.getBytes()));

        // Act
        DifyWorkflowResult result = difyApiClient.callDifyWorkflow(TEST_KEY, TEST_VALUE);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-id", result.getWorkflowRunId());
        assertEquals("failed", result.getStatus());
        
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void createWorkflowPayload_CreatesCorrectStructure() {
        // This test uses reflection to access the private method for testing
        try {
            java.lang.reflect.Method method = DifyApiClient.class.getDeclaredMethod("createWorkflowPayload", String.class, String.class);
            method.setAccessible(true);
            
            try (MockedStatic<com.mercaso.ims.infrastructure.util.SecurityUtil> securityUtilMock = mockStatic(com.mercaso.ims.infrastructure.util.SecurityUtil.class)) {
                securityUtilMock.when(com.mercaso.ims.infrastructure.util.SecurityUtil::getUserName).thenReturn("test-user");
                
                ObjectNode result = (ObjectNode) method.invoke(difyApiClient, TEST_KEY, TEST_VALUE);
                
                assertNotNull(result);
                assertTrue(result.has("inputs"));
                assertTrue(result.has("response_mode"));
                assertTrue(result.has("user"));
                assertEquals("streaming", result.get("response_mode").asText());
                assertEquals("test-user", result.get("user").asText());
                assertEquals(TEST_VALUE, result.get("inputs").get(TEST_KEY).asText());
            }
        } catch (Exception e) {
            fail("Failed to test createWorkflowPayload method: " + e.getMessage());
        }
    }

    @Test
    void buildWorkflowRequest_CreatesCorrectRequest() {
        // This test uses reflection to access the private method for testing
        try {
            java.lang.reflect.Method createPayloadMethod = DifyApiClient.class.getDeclaredMethod("createWorkflowPayload", String.class, String.class);
            createPayloadMethod.setAccessible(true);
            
            java.lang.reflect.Method buildRequestMethod = DifyApiClient.class.getDeclaredMethod("buildWorkflowRequest", ObjectNode.class);
            buildRequestMethod.setAccessible(true);
            
            try (MockedStatic<com.mercaso.ims.infrastructure.util.SecurityUtil> securityUtilMock = mockStatic(com.mercaso.ims.infrastructure.util.SecurityUtil.class)) {
                securityUtilMock.when(com.mercaso.ims.infrastructure.util.SecurityUtil::getUserName).thenReturn("test-user");
                
                ObjectNode payload = (ObjectNode) createPayloadMethod.invoke(difyApiClient, TEST_KEY, TEST_VALUE);
                Request request = (Request) buildRequestMethod.invoke(difyApiClient, payload);
                
                assertNotNull(request);
                assertEquals(TEST_BASE_URL + "/workflows/run", request.url().toString());
                assertEquals("Bearer " + TEST_API_KEY, request.header("Authorization"));
                assertEquals("application/json", request.header("Content-Type"));
                assertEquals("POST", request.method());
            }
        } catch (Exception e) {
            fail("Failed to test buildWorkflowRequest method: " + e.getMessage());
        }
    }
}
