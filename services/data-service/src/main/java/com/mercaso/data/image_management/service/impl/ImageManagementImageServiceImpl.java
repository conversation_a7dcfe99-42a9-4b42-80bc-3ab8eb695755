package com.mercaso.data.image_management.service.impl;

import static com.mercaso.data.master_catalog.exception.ErrorCodeEnums.UNSUPPORTED_IMAGE_MANAGEMENT_ITEM_IMAGE_MIME_TYPE;

import com.mercaso.data.image_management.entity.ImageManagementImage;
import com.mercaso.data.image_management.entity.ImageManagementItemImage;
import com.mercaso.data.image_management.enums.ImageTypeEnum;
import com.mercaso.data.image_management.exception.ImageManagementImageException;
import com.mercaso.data.image_management.repository.ImageManagementImageRepository;
import com.mercaso.data.image_management.repository.ImageManagementItemImageRepository;
import com.mercaso.data.image_management.service.ImageManagementImageService;
import com.mercaso.data.master_catalog.adaptor.ImsClientAdaptor;
import com.mercaso.data.master_catalog.constants.CommonConstants;
import com.mercaso.data.master_catalog.exception.ErrorCodeEnums;
import com.mercaso.document.operations.models.UploadDocumentRequest;
import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.ims.client.dto.ItemDto;
import com.mercaso.ims.client.dto.ItemUPCDto;
import com.mercaso.ims.client.dto.ItemUPCDto.ItemUpcTypeEnum;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import jakarta.activation.MimetypesFileTypeMap;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
@RequiredArgsConstructor
public class ImageManagementImageServiceImpl implements ImageManagementImageService {

  @Value("${mercaso.document.operations.storage.image-management.root-folder}")
  private String rootFolder;

  private final ImsClientAdaptor imsClientAdaptor;

  private final DocumentOperations documentOperations;

  private final ImageManagementImageRepository imageManagementRepository;

  private final ImageManagementItemImageRepository imageManagementItemImageRepository;

  private static final String IMAGE_PRIMARY_FLAG = "Front_2";

  private static final String ITEM_UPC_TYPE = "EACH_UPC";

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String uploadAndSave(String imagePath,
      ImageTypeEnum imageType, MultipartFile file) {

    // valid image path format
    validPath(imagePath);
    Path path = Paths.get(imagePath);
    Path parentDir = path.getParent();
    String date = parentDir.getParent().getFileName().toString();
    String upc = parentDir.getFileName().toString();
    String fileName = path.getFileName().toString();
    String angel = getAngel(imagePath, upc, fileName);
    String mimeType = isImageByMimeType(fileName);
    Boolean isPrimary = IMAGE_PRIMARY_FLAG.equals(angel);

    String imageKey = rootFolder + imageType.name() + File.separator + imagePath;
    Boolean exists = imageManagementRepository.existsByFilePath(imagePath);
    if (exists) {
      throw new ImageManagementImageException(ErrorCodeEnums.IMAGE_MANAGEMENT_ITEM_IMAGE_UPLOAD_ALREADY.getCode(),
          "Image of path:" + imagePath + " already exists");
    }

    try {
      UploadDocumentRequest documentRequest = UploadDocumentRequest
          .builder()
          .documentName(imageKey)
          .content(file.getBytes())
          .build();
      documentOperations.uploadDocument(documentRequest);
    } catch (Exception e) {
      log.error("Error while uploading image {} which path {}", file.getOriginalFilename(), imageKey, e);
      throw new ImageManagementImageException(ErrorCodeEnums.UPLOAD_IMAGE_MANAGEMENT_ITEM_IMAGE_FAILURE.getCode(),
          "Error while uploading image " + file.getOriginalFilename());
    }

    String userId = SecurityContextUtil.getLoginUserId();
    userId = userId == null ? CommonConstants.SYSTEM_USER_ID : userId;
    ImageManagementImage image = ImageManagementImage.builder()
        .fileName(fileName)
        .filePath(imageKey)
        .shotAt(toInstant(date, "yyyy-MM-dd"))
        .fileSize(file.getSize())
        .mimeType(mimeType)
        .createdBy(userId)
        .build();
    ImageManagementImage save = imageManagementRepository.save(image);

    List<ItemDto> items = imsClientAdaptor.searchItemDetailByUpc(upc);
    Boolean eachFlag = getEachFlag(upc, fileName, items);
    items.forEach(item -> {
      ImageManagementItemImage imageItem = ImageManagementItemImage
          .builder()
          .eachFlag(eachFlag)
          .imageId(save.getId())
          .imageAngel(angel)
          .isPrimary(isPrimary)
          .sku(item.getSkuNumber())
          .imageType(imageType.name())
          .build();
      imageManagementItemImageRepository.save(imageItem);
    });

    return ErrorCodeEnums.COMMON_CODE.getCode();
  }

  private String isImageByMimeType(String filename) {
    String mimeType = new MimetypesFileTypeMap().getContentType(filename);
    if (mimeType != null && mimeType.startsWith("image/")) {
      return mimeType;
    }
    throw new ImageManagementImageException(UNSUPPORTED_IMAGE_MANAGEMENT_ITEM_IMAGE_MIME_TYPE.getCode(),
        "unsupported mime type" + mimeType);
  }

  private Instant toInstant(String dateTimeStr, String pattern) {
    DateTimeFormatter formatter = new DateTimeFormatterBuilder().appendPattern(pattern)
        .parseDefaulting(ChronoField.MONTH_OF_YEAR, 1)
        .parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
        .parseDefaulting(ChronoField.HOUR_OF_DAY, 0)
        .parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0)
        .parseDefaulting(ChronoField.SECOND_OF_MINUTE, 0)
        .toFormatter();

    return LocalDateTime.parse(dateTimeStr, formatter)
        .atZone(ZoneId.systemDefault())
        .toInstant();
  }

  private String getAngel(String imagePath, String upc, String fileName) {
    String baseName = fileName.substring(0, fileName.lastIndexOf('.'));

    if (baseName.startsWith(upc + "_")) {
      String remaining = baseName.substring(upc.length() + 1);
      if (remaining.contains("_")) {
        if (remaining.indexOf('_') != remaining.lastIndexOf('_')) {
          int lastUnderscore = remaining.lastIndexOf('_');
          String suffix = remaining.substring(lastUnderscore + 1);
          return suffix.matches("\\d+") ? remaining.substring(0, lastUnderscore) : remaining;
        } else {
          return remaining;
        }
      }
    }

    log.error("Invalid image path {} for the file {}", imagePath, fileName);
    throw new ImageManagementImageException(ErrorCodeEnums.INVALID_IMAGE_MANAGEMENT_ITEM_IMAGE_PATH.getCode(),
        "Invalid image path " + imagePath);
  }

  private Boolean getEachFlag(String upc, String fileName, List<ItemDto> items) {
    Boolean eachFlag = null;
    //if items for current upc only contains one upc type,we get upc type from ims
    List<ItemUpcTypeEnum> upcTypeEnumList = items
        .stream()
        .map(ItemDto::getItemUPCs)
        .filter(Objects::nonNull)
        .flatMap(List::stream)
        .filter(itemUpc -> upc.equals(itemUpc.getUpcNumber()))
        .map(ItemUPCDto::getItemUpcType)
        .toList();
    if (upcTypeEnumList.size() == 1) {
      eachFlag = ITEM_UPC_TYPE.equals(upcTypeEnumList.get(0).getValue());
    } else {
      // if items for current upc contains more upc type,we get upc type from the file name
      String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
      if (baseName.startsWith(upc + "_")) {
        String remaining = baseName.substring(upc.length() + 1);
        if (remaining.contains("_")) {
          if (remaining.indexOf('_') != remaining.lastIndexOf('_')) {
            int lastUnderscore = remaining.lastIndexOf('_');
            String suffix = remaining.substring(lastUnderscore + 1);
            eachFlag = "1".equals(suffix);
          } else {
            eachFlag = false;
          }
        }
      }
    }
    return eachFlag;
  }

  private void validPath(String imagePath) {
    Pattern pattern = Pattern.compile("^/?(\\d{4}-\\d{1,2}-\\d{1,2})/(\\d+)/(\\2_[^/.]+\\.\\w+)$");
    if (!pattern.matcher(imagePath).matches()) {
      log.error("Invalid image path {}", imagePath);
      throw new ImageManagementImageException(ErrorCodeEnums.INVALID_IMAGE_MANAGEMENT_ITEM_IMAGE_PATH.getCode(),
          "Invalid image path " + imagePath);
    }
  }
}
