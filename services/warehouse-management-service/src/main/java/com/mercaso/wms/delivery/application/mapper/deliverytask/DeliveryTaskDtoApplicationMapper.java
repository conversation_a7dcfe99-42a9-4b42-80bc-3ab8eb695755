package com.mercaso.wms.delivery.application.mapper.deliverytask;

import com.mercaso.wms.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.wms.delivery.application.dto.deliverytask.DeliveryTaskDto;
import com.mercaso.wms.delivery.application.dto.deliverytask.PostCheckDto;
import com.mercaso.wms.delivery.application.dto.deliverytask.PreCheckDto;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.io.IOException;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DeliveryTaskDtoApplicationMapper extends BaseDtoApplicationMapper<DeliveryTask, DeliveryTaskDto> {

    DeliveryTaskDtoApplicationMapper INSTANCE = Mappers.getMapper(DeliveryTaskDtoApplicationMapper.class);

    @Mapping(target = "preCheck", expression = "java(mapPreCheck(deliveryTask))")
    @Mapping(target = "postCheck", expression = "java(mapPostCheck(deliveryTask))")
    DeliveryTaskDto domainToDto(DeliveryTask deliveryTask);

    default PreCheckDto mapPreCheck(DeliveryTask deliveryTask) {
        if (deliveryTask == null || deliveryTask.getPreCheck() == null) {
            return null;
        }
        try {
            return SerializationUtils.deserialize(deliveryTask.getPreCheck(), PreCheckDto.class);
        } catch (IOException e) {
            throw new DeliveryBusinessException("Failed to deserialize PreCheckDto", e);
        }
    }

    default PostCheckDto mapPostCheck(DeliveryTask deliveryTask) {
        if (deliveryTask == null || deliveryTask.getPostCheck() == null) {
            return null;
        }
        try {
            return SerializationUtils.deserialize(deliveryTask.getPostCheck(), PostCheckDto.class);
        } catch (IOException e) {
            throw new DeliveryBusinessException("Failed to deserialize PostCheckDto", e);
        }
    }
} 