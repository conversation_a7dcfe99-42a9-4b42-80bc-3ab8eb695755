package com.mercaso.wms.infrastructure.repository.crossdockitem.jpa.mapper;

import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import com.mercaso.wms.infrastructure.repository.BaseDoMapper;
import com.mercaso.wms.infrastructure.repository.crossdockitem.jpa.dataobject.CrossDockTaskItemDo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CrossDockTaskItemMapper extends BaseDoMapper<CrossDockTaskItemDo, CrossDockTaskItem> {

}