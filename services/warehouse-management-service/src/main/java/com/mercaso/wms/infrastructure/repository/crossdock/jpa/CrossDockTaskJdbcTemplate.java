package com.mercaso.wms.infrastructure.repository.crossdock.jpa;

import com.mercaso.wms.application.dto.view.SearchCrossDockTaskView;
import com.mercaso.wms.infrastructure.repository.crossdock.jpa.criteria.CrossDockTaskSearchCriteria;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
@Slf4j
public class CrossDockTaskJdbcTemplate {

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public Page<SearchCrossDockTaskView> search(CrossDockTaskSearchCriteria criteria, Pageable pageable) {
        StringBuilder sql = new StringBuilder();
        StringBuilder countSql = new StringBuilder();
        MapSqlParameterSource params = new MapSqlParameterSource();

        sql.append(
                "SELECT distinct cdt.id as id, cdt.number as number, cdt.picker_user_name as picker_user_name, total_qty, cdt.created_at as created_at, cdt.updated_at as updated_at, ")
            .append("SUM(cdt_items.cross_docked_qty) OVER(PARTITION BY cdt.id) as total_cross_docked_qty, ")
            .append("SUM(cdt_items.picked_qty) OVER(PARTITION BY cdt.id) as total_picked_qty ")
            .append("FROM cross_dock_task cdt ")
            .append("LEFT JOIN cross_dock_task_items cdt_items on cdt.id = cdt_items.cross_dock_task_id ")
            .append("LEFT JOIN batch b on cdt_items.batch_id = b.id ")
            .append("WHERE cdt.deleted_at IS NULL ");

        countSql.append("SELECT COUNT(DISTINCT cdt.id) ")
            .append("FROM cross_dock_task cdt ")
            .append("LEFT JOIN cross_dock_task_items cdt_items on cdt.id = cdt_items.cross_dock_task_id ")
            .append("LEFT JOIN batch b on cdt_items.batch_id = b.id ")
            .append("WHERE cdt.deleted_at IS NULL ");

        if (StringUtils.isNotBlank(criteria.getDeliveryDate())) {
            sql.append("AND b.tag = :deliveryDate ");
            countSql.append("AND b.tag = :deliveryDate ");
            params.addValue("deliveryDate", criteria.getDeliveryDate());
        }

        if (criteria.getPickerUserId() != null) {
            sql.append("AND (cdt.picker_user_id = :pickerUserId) ");
            countSql.append("AND (cdt.picker_user_id = :pickerUserId) ");
            params.addValue("pickerUserId", criteria.getPickerUserId());
        }

        if (StringUtils.isNotBlank(criteria.getNumber())) {
            sql.append("AND (cdt.number = :number) ");
            countSql.append("AND (cdt.number = :number) ");
            params.addValue("number", criteria.getNumber());
        }
        sql.append("ORDER BY cdt.created_at DESC ");
        sql.append(" LIMIT :limit OFFSET :offset");

        params.addValue("limit", pageable.getPageSize());
        params.addValue("offset", pageable.getOffset());

        Long total = jdbcTemplate.queryForObject(countSql.toString(), params, Long.class);

        List<SearchCrossDockTaskView> content = jdbcTemplate.query(sql.toString(), params,
            (rs, rowNum) -> mapToCrossDockTaskView(rs));

        return new PageImpl<>(content, pageable, total != null ? total : 0);
    }

    private SearchCrossDockTaskView mapToCrossDockTaskView(ResultSet rs) throws SQLException {
        SearchCrossDockTaskView view = new SearchCrossDockTaskView();
        String id = rs.getString("id");
        view.setId(id != null ? UUID.fromString(id) : null);
        view.setNumber(rs.getString("number"));
        view.setPickerUserName(rs.getString("picker_user_name"));
        view.setTotalPickedQty(rs.getInt("total_picked_qty"));
        view.setTotalCrossDockedQty(rs.getInt("total_cross_docked_qty"));
        view.setCreatedAt(rs.getTimestamp("created_at").toInstant());
        view.setUpdatedAt(rs.getTimestamp("updated_at").toInstant());
        return view;
    }
}