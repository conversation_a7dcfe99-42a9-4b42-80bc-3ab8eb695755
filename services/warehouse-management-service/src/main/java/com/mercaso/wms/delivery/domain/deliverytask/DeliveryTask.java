package com.mercaso.wms.delivery.domain.deliverytask;

import static com.mercaso.wms.infrastructure.utils.DateUtils.secondsSinceMidnight;

import com.mercaso.wms.delivery.application.command.deliverytask.UpdateDeliveryTaskCommand;
import com.mercaso.wms.delivery.application.service.DeliveryTaskService.DriverProperties;
import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskTransitionEvents;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ApprovedRoute;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.enums.OrderStepType;
import com.mercaso.wms.infrastructure.statemachine.BaseStateMachine;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.time.Instant;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Configurable;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
@Slf4j
public class DeliveryTask extends BaseStateMachine<DeliveryTask, DeliveryTaskStatus, DeliveryTaskTransitionEvents> {

    private final UUID id;
    private String number;
    private String deliveryDate;
    private String truckNumber;
    private UUID driverUserId;
    private String driverUserName;
    private Instant clockIn;
    private Instant dispatchAt;
    private Instant breakStartAt;
    private Instant breakEndAt;
    private Instant completedAt;
    private Boolean buildHasException;
    private String preCheck;
    private String postCheck;

    public DeliveryTask create(ApprovedRoute route, UUID driverUserId, String number) {
        this.deliveryDate = route.getRoute().getDate();
        this.truckNumber = route.getVehicle().getExternalId();
        this.driverUserId = driverUserId;
        this.driverUserName = route.getDriver().getName();
        this.setState(DeliveryTaskStatus.CREATED);
        this.updateBreak(route);
        this.buildHasException = false;
        this.number = number;
        return this;
    }

    public void updateBreak(ApprovedRoute approvedRoute) {
        approvedRoute.getRoute().getSteps().stream().filter(step -> OrderStepType.BRK.getValue().equals(step.getType()))
            .findFirst()
            .ifPresent(step -> {
                this.breakStartAt = secondsSinceMidnight(approvedRoute.getRoute().getDate(), step.getStartSec());
                this.breakEndAt = secondsSinceMidnight(approvedRoute.getRoute().getDate(), step.getEndSec());
            });
    }

    public void updateDriver(Account account) {
        this.driverUserId = account.getId();
        this.driverUserName = account.getUserName();
    }

    public void assignDriverToTask(DriverProperties driverProperties) {
        this.driverUserId = driverProperties.driverUserId();
        this.driverUserName = driverProperties.driverName();
    }

    private void inProgress() {
        this.dispatchAt = Instant.now();
        this.processEvent(DeliveryTaskTransitionEvents.MARK_AS_IN_PROGRESS);
    }

    /**
     * Marks the delivery task as completed
     * Sets the completed timestamp to the current time
     */
    private void complete() {

        log.info("Completing delivery task {}", this.id);

        this.processEvent(DeliveryTaskTransitionEvents.MARK_AS_COMPLETED);
        this.completedAt = Instant.now();
    }

    public void updateStatus(DeliveryTaskStatus newStatus) {
        if (this.getState() == newStatus) {
            log.warn("Delivery task {} already in status {}", this.getTruckNumber(), newStatus);
            return;
        }

        switch (newStatus) {
            case DeliveryTaskStatus.IN_PROGRESS -> this.inProgress();
            case DeliveryTaskStatus.COMPLETED -> this.complete();
            default -> throw new IllegalArgumentException(
                "Unsupported status transition to " + newStatus.name());
        }
    }

    public void update(UpdateDeliveryTaskCommand command) {
        if (command.getPreCheck() != null) {
            this.preCheck = SerializationUtils.serialize(command.getPreCheck());
        }
        if (command.getPostCheck() != null) {
            this.postCheck = SerializationUtils.serialize(command.getPostCheck());
        }
    }
} 