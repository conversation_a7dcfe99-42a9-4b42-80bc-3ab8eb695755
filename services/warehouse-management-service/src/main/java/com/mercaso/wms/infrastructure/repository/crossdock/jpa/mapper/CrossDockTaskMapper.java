package com.mercaso.wms.infrastructure.repository.crossdock.jpa.mapper;

import com.mercaso.wms.domain.crossdock.CrossDockTask;
import com.mercaso.wms.infrastructure.repository.BaseDoMapper;
import com.mercaso.wms.infrastructure.repository.crossdock.jpa.dataobject.CrossDockTaskDo;
import com.mercaso.wms.infrastructure.repository.crossdockitem.jpa.mapper.CrossDockTaskItemMapper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
    uses = CrossDockTaskItemMapper.class,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CrossDockTaskMapper extends BaseDoMapper<CrossDockTaskDo, CrossDockTask> {

}
