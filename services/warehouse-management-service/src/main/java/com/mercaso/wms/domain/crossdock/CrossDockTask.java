package com.mercaso.wms.domain.crossdock;

import com.mercaso.wms.domain.BaseDomain;
import com.mercaso.wms.domain.crossdock.enums.CrossDockTaskStatusEnum;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import java.util.List;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.beans.factory.annotation.Configurable;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
public class CrossDockTask extends BaseDomain {

    private final UUID id;

    private String number;

    private UUID warehouseId;

    private CrossDockTaskStatusEnum status;

    private UUID pickerUserId;

    private String pickerUserName;

    private Integer totalQty;

    private List<CrossDockTaskItem> crossDockTaskItems;

} 