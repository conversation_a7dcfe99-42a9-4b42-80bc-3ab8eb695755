package com.mercaso.wms.application.mapper.crossdock;

import com.mercaso.wms.application.dto.CrossDockItemDto;
import com.mercaso.wms.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CrossDockTaskItemDtoApplicationMapper extends BaseDtoApplicationMapper<CrossDockTaskItem, CrossDockItemDto> {

    CrossDockTaskItemDtoApplicationMapper INSTANCE = Mappers.getMapper(CrossDockTaskItemDtoApplicationMapper.class);


}
