package com.mercaso.wms.infrastructure.repository.crossdock;

import com.mercaso.wms.application.dto.view.SearchCrossDockTaskView;
import com.mercaso.wms.domain.crossdock.CrossDockTask;
import com.mercaso.wms.domain.crossdock.CrossDockTaskRepository;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.crossdock.jpa.CrossDockTaskJdbcTemplate;
import com.mercaso.wms.infrastructure.repository.crossdock.jpa.criteria.CrossDockTaskSearchCriteria;
import com.mercaso.wms.infrastructure.repository.crossdock.jpa.dataobject.CrossDockTaskDo;
import com.mercaso.wms.infrastructure.repository.crossdock.jpa.mapper.CrossDockTaskMapper;
import com.mercaso.wms.infrastructure.repository.crossdockitem.jpa.CrossDockTaskItemJpaDao;
import com.mercaso.wms.infrastructure.repository.crossdockitem.jpa.CrossDockTaskJpaDao;
import com.mercaso.wms.infrastructure.repository.crossdockitem.jpa.dataobject.CrossDockTaskItemDo;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Slf4j
@Repository
@RequiredArgsConstructor
@Transactional
public class CrossDockTaskRepositoryImpl implements CrossDockTaskRepository {

    private final CrossDockTaskMapper mapper;
    private final CrossDockTaskJpaDao crossDockTaskJpaDao;
    private final CrossDockTaskItemJpaDao crossDockTaskItemJpaDao;
    private final CrossDockTaskJdbcTemplate crossDockTaskJdbcTemplate;

    @Override
    public CrossDockTask save(CrossDockTask domain) {
        CrossDockTaskDo crossDockTaskDo = mapper.domainToDo(domain);
        crossDockTaskDo.getCrossDockTaskItems()
            .forEach(crossDockTaskItemDo -> crossDockTaskItemDo.setCrossDockTaskId(crossDockTaskDo.getId()));
        return mapper.doToDomain(crossDockTaskJpaDao.saveAndFlush(crossDockTaskDo));
    }

    @Override
    public CrossDockTask findById(UUID id) {
        Optional<CrossDockTaskDo> byId = crossDockTaskJpaDao.findById(id);
        return byId.map(mapper::doToDomain).orElse(null);
    }

    @Override
    public CrossDockTask update(CrossDockTask domain) {
        CrossDockTaskDo crossDockTaskDo = crossDockTaskJpaDao.findById(domain.getId())
            .orElseThrow(() -> new WmsBusinessException("crossDockTask not found."));
        CrossDockTaskDo target = mapper.domainToDo(domain);

        List<CrossDockTaskItemDo> needRemoveItems = new LinkedList<>();
        crossDockTaskDo.getCrossDockTaskItems().forEach(crossDockTaskItemDo -> {
            if (target.getCrossDockTaskItems()
                .stream()
                .noneMatch(item -> item.getId().equals(crossDockTaskItemDo.getId()))) {
                needRemoveItems.add(crossDockTaskItemDo);
            }
        });

        target.getCrossDockTaskItems()
            .forEach(crossDockTaskItemDo -> crossDockTaskItemDo.setCrossDockTaskId(target.getId()));
        List<String> ignoreProperties = List.of("createdBy", "createdAt", "createdUserName");
        BeanUtils.copyProperties(target, crossDockTaskDo, ignoreProperties.toArray(new String[0]));

        crossDockTaskItemJpaDao.deleteAll(needRemoveItems);
        return mapper.doToDomain(crossDockTaskJpaDao.save(crossDockTaskDo));
    }

    @Override
    public Page<SearchCrossDockTaskView> search(CrossDockTaskSearchCriteria criteria, Pageable pageable) {
        return crossDockTaskJdbcTemplate.search(criteria, pageable);
    }

    @Override
    public List<CrossDockTask> saveAll(List<CrossDockTask> crossDockTasks) {
        List<CrossDockTaskDo> crossDockTaskDos = mapper.domainToDos(crossDockTasks);
        crossDockTaskDos = crossDockTaskJpaDao.saveAll(crossDockTaskDos);

        crossDockTaskDos.forEach(crossDockTaskDo -> {
            if (!CollectionUtils.isEmpty(crossDockTaskDo.getCrossDockTaskItems())) {
                crossDockTaskDo.getCrossDockTaskItems()
                    .forEach(crossDockTaskItemDo -> crossDockTaskItemDo.setCrossDockTaskId(crossDockTaskDo.getId()));
                crossDockTaskItemJpaDao.saveAll(crossDockTaskDo.getCrossDockTaskItems());
            }
        });

        return mapper.doToDomains(crossDockTaskDos);
    }

    @Override
    public void deleteAll() {
        crossDockTaskJpaDao.deleteAll();
    }
}