package com.mercaso.wms.application.service;

import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItemRepository;
import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItemRepository;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CrossDockTaskItemService {

    private final CrossDockTaskItemRepository crossDockTaskItemRepository;
    private final PickingTaskItemRepository pickingTaskItemRepository;
    private final ReceivingTaskItemRepository receivingTaskItemRepository;

    @Async
    public void handleTaskItemQtyChange(Map<UUID, Integer> qtyChangeMap, CrossDockItemSourceEnum source) {
        if (qtyChangeMap == null || qtyChangeMap.isEmpty()) return;
        List<UUID> ids = new ArrayList<>(qtyChangeMap.keySet());
        if (source == CrossDockItemSourceEnum.PICKING_TASK) {
            Map<UUID, PickingTaskItem> itemMap = pickingTaskItemRepository.findByIds(ids).stream().collect(Collectors.toMap(PickingTaskItem::getId, x -> x));
            processCrossDockItems(itemMap, qtyChangeMap, (item, qty) -> CrossDockTaskItem.builder().build().create(item, qty));
        } else if (source == CrossDockItemSourceEnum.RECEIVING_TASK) {
            Map<UUID, ReceivingTaskItem> itemMap = new HashMap<>();
            for (UUID id : ids) {
                ReceivingTaskItem item = receivingTaskItemRepository.findById(id);
                if (item != null) {
                    itemMap.put(id, item);
                }
            }
            processCrossDockItems(itemMap, qtyChangeMap, (item, qty) -> CrossDockTaskItem.builder().build().create(item, qty));
        }
    }

    private <T> void processCrossDockItems(
            Map<UUID, T> itemMap,
            Map<UUID, Integer> qtyChangeMap,
            BiFunction<T, Integer, CrossDockTaskItem> adapter
    ) {
        qtyChangeMap.keySet().stream()
            .filter(id -> !itemMap.containsKey(id))
            .forEach(id -> log.warn("[CrossDockTaskItemService] TaskItem not found for id={}", id));

        qtyChangeMap.entrySet().stream()
            .filter(entry -> itemMap.containsKey(entry.getKey()))
            .filter(entry -> entry.getValue() > 0)
            .forEach(entry -> createOrUpdateCrossDockItem(adapter.apply(itemMap.get(entry.getKey()), entry.getValue())));
    }

    private void createOrUpdateCrossDockItem(CrossDockTaskItem item) {
        try {
            crossDockTaskItemRepository.findByTaskItemId(item.getTaskItemId())
                .stream()
                .findFirst()
                .ifPresentOrElse(
                    exist -> {
                        exist.setPickedQty(item.getPickedQty());
                        crossDockTaskItemRepository.update(exist);
                        log.info("[CrossDockTaskItemService] Cross dock item updated for taskItemId={}, pickedQty={}", item.getTaskItemId(), item.getPickedQty());
                    },
                    () -> {
                        CrossDockTaskItem saved = crossDockTaskItemRepository.save(item);
                        log.info("[CrossDockTaskItemService] Cross dock item created for taskItemId={}, pickedQty={}, id={}",
                            saved.getTaskItemId(),
                            saved.getPickedQty(),
                            saved.getId());
                    }
                );
        } catch (Exception e) {
            log.error("[CrossDockTaskItemService] Failed to create/update cross dock item for taskItemId={}", item.getTaskItemId(), e);
        }
    }
} 