package com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.dataobject;

import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.time.Instant;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "da_delivery_task")
@SQLRestriction("deleted_at is null")
public class DeliveryTaskDo extends BaseDo {

    private String number;

    @Column(name = "delivery_date", length = 50)
    private String deliveryDate;

    @Column(name = "status", length = 50)
    @Enumerated(EnumType.STRING)
    private DeliveryTaskStatus status;

    @Column(name = "truck_number", length = 50)
    private String truckNumber;

    @Column(name = "driver_user_id")
    private UUID driverUserId;

    @Column(name = "driver_user_name", length = 100)
    private String driverUserName;

    @Column(name = "clock_in")
    private Instant clockIn;

    @Column(name = "dispatch_at")
    private Instant dispatchAt;

    @Column(name = "break_start_at")
    private Instant breakStartAt;

    @Column(name = "break_end_at")
    private Instant breakEndAt;

    @Column(name = "completed_at")
    private Instant completedAt;

    @Column(name = "build_has_exception")
    private Boolean buildHasException;

    @Column(name = "pre_check")
    private String preCheck;

    @Column(name = "post_check")
    private String postCheck;
} 