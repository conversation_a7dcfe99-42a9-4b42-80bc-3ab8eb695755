package com.mercaso.wms.application.dto;

import com.mercaso.wms.domain.crossdock.enums.CrossDockTaskStatusEnum;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CrossDockTaskDto extends BaseDto {

    private UUID id;

    private String number;

    private UUID warehouseId;

    private CrossDockTaskStatusEnum status;

    private UUID pickerUserId;

    private String pickerUserName;

    private Integer totalQty;

    private Instant createdAt;

    private String createdBy;

    private Instant updatedAt;

    private String updatedBy;

    private List<CrossDockItemDto> items;
} 