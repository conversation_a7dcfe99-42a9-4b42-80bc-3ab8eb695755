package com.mercaso.wms.delivery.application.event.dto;

import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.delivery.application.dto.deliverytask.PostCheckDto;
import com.mercaso.wms.delivery.application.dto.deliverytask.PreCheckDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryTaskUpdatedEventDto extends BaseDto {

    private PreCheckDto preCheck;

    private PostCheckDto postCheck;

}
