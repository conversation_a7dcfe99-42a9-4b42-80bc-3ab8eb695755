package com.mercaso.wms.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.view.SearchCrossDockTaskView;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class CrossDockTaskResourceApi extends IntegrationTestRestUtil {

    private static final String SEARCH_CROSS_DOCK_TASKS_URL = "/search/cross-dock-tasks";

    public CrossDockTaskResourceApi(Environment environment) {
        super(environment);
    }

    public Result<SearchCrossDockTaskView> searchCrossDockTasks(
        String number,
        UUID pickerUserId,
        String deliveryDate) throws Exception {

        StringBuilder urlBuilder = new StringBuilder(SEARCH_CROSS_DOCK_TASKS_URL).append("?");
        if (StringUtils.isNotBlank(number)) {
            urlBuilder.append("number=").append(number).append("&");
        }
        if (pickerUserId != null) {
            urlBuilder.append("pickerUserId=").append(pickerUserId).append("&");
        }
        if (StringUtils.isNotBlank(deliveryDate)) {
            urlBuilder.append("deliveryDate=").append(deliveryDate);
        }
        String url =
            urlBuilder.toString().endsWith("&") ? urlBuilder.substring(0, urlBuilder.length() - 1) : urlBuilder.toString();

        String body = getEntity(url, String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<>() {
        });
    }

}